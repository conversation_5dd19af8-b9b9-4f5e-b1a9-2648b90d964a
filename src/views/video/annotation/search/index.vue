<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <!-- 工具栏自定义按钮 -->
    <template #toolbar:after>
      <!-- 无额外工具栏按钮 -->
    </template>

    <!-- 视频封面列自定义渲染 -->
    <template #table:value30:simple="{ row }">
      <el-image
        v-if="row.value30"
        :src="row.value30"
        :preview-src-list="[row.value30]"
        style="width: 60px; height: 40px"
        fit="cover"
        preview-teleported
      />
      <span v-else>暂无封面</span>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:before="{ row }">
      <el-button type="text" size="mini" @click="handleRefresh(row)">
        刷新
      </el-button>
      <el-button type="text" size="mini" @click="handleExport(row)">下载</el-button>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  name: 'VideoAnnotationSearch',
  data() {
    return {
      tableType: 'video_file_management'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '搜索记录',
        tableActionProps: {
          width: 200
        },
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 视频唯一编号
          value1: {
            type: 'text',
            label: '视频唯一编号',
            align: 'left',
            fixed: 'left',
            search: {
              hidden: true
            }
          },
          // 视频封面
          value30: {
            type: 'text',
            label: '视频封面',
            search: {
              hidden: true
            }
          },
          // 搜索内容
          value31: {
            type: 'text',
            label: '搜索内容',
            showOverflowTooltip: true,
            search: {
              type: 'input',
              placeholder: '请输入搜索内容'
            }
          },
          // 匹配度
          value32: {
            type: 'text',
            label: '匹配度',
            search: {
              hidden: true
            }
          },
          // 搜索时间
          value33: {
            type: 'text',
            label: '搜索时间',
            search: {
              hidden: true
            }
          },
          // 以图搜索
          value34: {
            type: 'text',
            label: '以图搜索',
            table: { hidden: true },
            search: {
              type: 'upload',
              placeholder: '请上传图片进行搜索',
              fieldProps: {
                accept: 'image/*',
                listType: 'picture-card',
                limit: 1
              }
            },
            form: { hidden: true }
          }
        }
      }
    }
  },
  methods: {
    // 查看操作
    handleView(row) {
      this.$refs.sheetRef.handleInfo(row)
    },

    // 刷新操作
    handleRefresh(row) {
      this.$refs.sheetRef.getTableData()
      this.$modal.msgSuccess('刷新成功')
    },

    // 导出操作
    handleExport(row) {
      this.$modal.confirm(`确认要导出视频"${row.value1}"的搜索结果吗？`).then(() => {
        // 这里可以调用导出接口
        console.log('导出搜索结果:', row)
        this.$modal.msgSuccess('导出成功')
      }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
